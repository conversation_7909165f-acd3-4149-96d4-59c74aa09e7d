/*
 *
 * UserListing constants
 *
 */

export const UPDATE_FIELD = 'app/UserListing/UPDATE_FIELD';
export const USER_LIST_FORM_KEY = 'userListing';
export const ACCEPTED_FILE_TYPES = [
  '.csv',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
];
export const LEAVE_TYPES = [
  {
    label: 'Sick or Casual Leave',
    value: 'Sick or Casual Leave',
  },
  {
    label: 'Paid Leave',
    value: 'Paid Leave',
  },
  {
    label: 'Leave Without Pay',
    value: 'Leave Without Pay',
  },
  {
    label: 'Compensatory Off',
    value: 'Compensatory Off',
  },
  {
    label: 'Comp off',
    value: 'Comp off',
  },
  {
    label: 'Holiday',
    value: 'Holiday',
  },
  {
    label: 'Paternity Leave',
    value: 'Paternity Leave',
  },
  {
    label: 'Comp off',
    value: 'Comp off',
  },
  {
    label: 'Optional Holiday',
    value: 'Optional Holiday',
  },
  {
    label: 'Sick Leave',
    value: 'Sick Leave',
  },
  {
    label: 'Casual Leave',
    value: 'Casual Leave',
  },
  {
    label: 'Maternity Leave',
    value: 'Maternity Leave',
  },
  {
    label: 'Present ( Paid Leave  )',
    value: 'Present ( Paid Leave  )',
  },
  {
    label: 'Present ( Leave Without Pay  )',
    value: 'Present ( Leave Without Pay  )',
  },
  {
    label: 'Present ( Compensatory Off  )',
    value: 'Present ( Compensatory Off  )',
  },
  {
    label: 'Present ( Holiday  )',
    value: 'Present ( Holiday  )',
  },
  {
    label: 'Present ( Sick Leave  )',
    value: 'Present ( Sick Leave  )',
  },
  {
    label: 'Present ( Casual Leave  )',
    value: 'Present ( Casual Leave  )',
  },
  {
    label: 'Present ( Paid Leave )',
    value: 'Present ( Paid Leave )',
  },
  {
    label: 'Present ( Leave Without Pay )',
    value: 'Present ( Leave Without Pay )',
  },
  {
    label: 'Present ( Compensatory Off )',
    value: 'Present ( Compensatory Off )',
  },
  {
    label: 'Present ( Holiday )',
    value: 'Present ( Holiday )',
  },
  {
    label: 'Present ( Sick Leave )',
    value: 'Present ( Sick Leave )',
  },
  {
    label: 'Present ( Casual Leave )',
    value: 'Present ( Casual Leave )',
  },
];

export const ALL_LEAVE_DURATION = [
  {
    label: 'Full Day',
    value: 'full',
  },
  {
    label: 'First Half',
    value: 'first',
  },
  {
    label: 'Second Half',
    value: 'second',
  },
];

export const FIRST_HALF_LEAVE_DURATION = [
  {
    label: 'Full Day',
    value: 'full',
  },
  {
    label: 'First Half',
    value: 'first',
  },
];

export const SECOND_HALF_LEAVE_DURATION = [
  {
    label: 'Full Day',
    value: 'full',
  },
  {
    label: 'Second Half',
    value: 'second',
  },
];

export const REPORT_TYPE = {
  ATTENDANCE: {
    label: 'Attendance Report',
    value: 'attendance',
  },
  PERSON_DAY: {
    label: 'Person Day Report',
    value: 'personDay',
  },
  PROJECT_WISE_PERSON_HOURS: {
    label: 'Project Wise Person Hours Report',
    value: 'projectWisePersonHours',
  },
  ATTENDANCE_LOGS: {
    label: 'Attendance Logs Report',
    value: 'attendanceLogsReport',
  },
  BILLING_SHEET: {
    label: 'Billing Sheet Report',
    value: 'billingSheetReport',
  },
  REVIEW_EMPLOYEES_LOGS: {
    label: 'Review Employees Logs',
    value: 'reviewEmployeesLogs',
  },
  MANAGER_LOGS_REPORT: {
    label: 'Manager logs report',
    value: 'managerLogsReport',
  },
};

export const DropDownStyle = {
  control: (provided, state) => ({
    ...provided,
    borderRadius: '2px',
    minHeight: '31px',
    boxShadow: state.isFocused ? '0 0 0 2px rgba(77, 24, 110, 0.2)' : null,
    borderColor: state.isFocused ? '#4d186e' : '#d9d9d9',

    height: '31px',
    ':hover': {
      borderColor: '#4d186e',
      outline: 0,
      boxShadow: 'none',
      borderRightWidth: '1px !important',
    },
  }),

  option: (styles, { isSelected }) => ({
    ...styles,
    color: 'rgba(0, 0, 0, 0.85)',
    fontWeight: isSelected ? 600 : '',
    backgroundColor: isSelected ? '#aba3ad' : '',
    ':active': {
      backgroundColor: '#f5f5f5',
    },
  }),

  valueContainer: provided => ({
    ...provided,
    height: '31px',
    background: '4d186e',
  }),

  input: provided => ({
    ...provided,
    margin: '0px',
    '::placeholder': {
      color: 'red',
    },
  }),

  placeholder: defaultStyles => ({
    ...defaultStyles,
    color: '#bfbfbf',
  }),

  indicatorsContainer: provided => ({
    ...provided,
    height: '31px',
  }),
};
