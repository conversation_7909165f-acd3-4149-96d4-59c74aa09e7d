const mongoose = require('mongoose');
const Project = require('../../models/project.model');
const Constants = require('../../models/constants.model');
const User = require('../../models/user.model');
const BusinessUnit = require('../../models/bu.model');
const Utils = require('../../util/utilFunctions');
const { parse } = require('json2csv');
const UploadProjectHeaderValidator = require('./uploadProjectHeaderValidator');
const xlsx = require('node-xlsx').default;
/**
 * Class represents services for download project wise employee attendance.
 */
class DownloadProjectPersonHoursReportService {

    /**
     * @desc This function is being used to download project person days report
     * <AUTHOR>
     * @since 22/06/2021
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async downloadProjectPersonHoursReport (req, user) {
        const where = {
            role: { $ne: 4 },
            label: { $exists: true, $type: 2 },
        };
        let buDetails = '';
        if (user.role === CONSTANTS.ROLE.BU) {
            buDetails = await BusinessUnit.findOne({ userId: user._id }, { _id: 1 }).lean();
        }
        let projects = [];
        const isUserManager = await Project.countDocuments(
            {
                $or: [{
                    pmUser: { $eq: user._id }
                }, {
                    reviewManager: { $eq: user._id }
                }, {
                    businessUnitId: { $eq: buDetails?._id }
                }]
            });
        if (user.role === CONSTANTS.ROLE.ADMIN) {
            if (req.query.userId) {
                where._id = mongoose.Types.ObjectId(req.query.userId);
            }

            if (req.query.projectId) {
                projects = req.query.projectId.split(',');
            }
        } else if (isUserManager) {
            if (req.query.userId) {
                where._id = mongoose.Types.ObjectId(req.query.userId);
            }
            if (req.query.projectId) {
                projects = req.query.projectId.split(',');
            }
        } else {
            // Do nothing
        }

        const startDate = (req.query.startDate) ? MOMENT(req.query.startDate).startOf('day')
            : MOMENT().startOf('month');
        const endDate = (req.query.endDate) ? MOMENT(req.query.endDate).endOf('day')
            : MOMENT().endOf('month');

        const aggregateParams = DownloadProjectPersonHoursReportService.getAggeagateParams(where, startDate._d, endDate._d, projects);
        const logs = await User.aggregate(aggregateParams);
        const totalRow = {
            'name': 'Total',
            'total': 0
        };

        const projectNames = [];

        logs.forEach(l => {
            l.logs.forEach((p) => {
                totalRow[p._id] = 0;
                projectNames.push({
                    label: p._id,
                    value: p._id
                });
            });
        });

        const formattedLogs = [];
        logs.forEach((l) => {
            let total = 0;

            const log = {
                name: l.name,
                total: 0
            };

            l.logs.forEach((p) => {
                log[p._id] = Utils.roundToTwo(p.logs);
                total += Utils.roundToTwo(p.logs);
                totalRow[p._id] += Utils.roundToTwo(p.logs);
            });

            log.total = total;
            totalRow.total += total;
            formattedLogs.push(log);
        });


        formattedLogs.push(totalRow);
        const fields = _.uniqBy(projectNames, 'label');

        for (const [key, value] of Object.entries(totalRow)) {
            fields.map(f => {
                if (f.label === key) {
                    f.totolHours = value;
                }
            });
        }

        const constantsFromDb = await Constants.findOne();
        const projectSet = constantsFromDb.projectSetForPersonHoursReport ? constantsFromDb.projectSetForPersonHoursReport : [];

        fields.sort((a, b) => {
            return b.totolHours - a.totolHours;
        });


        const matchingFields = [];
        const nonMatchingFields = [];

        const lowerCaseTrimmed = projectSet.map((name) => name.replace(/\s+/g, '').toLowerCase().trim());
        fields.forEach((field) => {
            if (lowerCaseTrimmed.includes(field.label.replace(/\s+/g, '').toLowerCase().trim())) {
                matchingFields.push(field);
            } else {
                nonMatchingFields.push(field);
            }
        });

        matchingFields.sort((a, b) => {
            const indexA = lowerCaseTrimmed.indexOf(a.label.replace(/\s+/g, '').toLowerCase().trim());
            const indexB = lowerCaseTrimmed.indexOf(b.label.replace(/\s+/g, '').toLowerCase().trim());
            return indexA - indexB;
        });

        nonMatchingFields.sort((a, b) => a.label.localeCompare(b.label));

        const filteredFields = [...matchingFields, ...nonMatchingFields];
        filteredFields.unshift({
            label: 'Name',
            value: 'name'
        });
        filteredFields.push({
            label: 'Total',
            value: 'total'
        });
        const opts = { fields: filteredFields, quote: '' };
        const csvData = parse(formattedLogs, opts);
        return {
            headers: [{
                key: 'Content-Type',
                value: 'text/csv'
            }, {
                key: 'Content-Disposition',
                value: 'attachment; filename=projectpersondays.csv'
            }],
            data: csvData
        };
    }

    static getAggeagateParams (where, startDate, endDate, projectNames) {
        const logWhere = {
            $and: [{
                $eq: ['$userId', '$$uId']
            }, {
                $gte: ['$logDate', startDate]
            }, {
                $lte: ['$logDate', endDate]
            }]
        };

        if (projectNames.length) {
            logWhere.$and.push({ $in: ['$jiraProjectName', projectNames] });
        }

        return [{
            $match: where
        }, {
            $project: {
                employeeId: 1,
                name: { $concat: ['$firstName', ' ', '$lastName'] }
            }
        }, {
            $lookup: {
                from: 'logs',
                let: {
                    uId: '$_id'
                },
                pipeline: [{
                    $match: {
                        $expr: logWhere
                    }
                }, {
                    $project: {
                        _id: 1,
                        userId: 1,
                        projectId: 1,
                        timeSpentHours: 1,
                        logDate: 1,
                        jiraProjectName: 1
                    }
                }, {
                    $group: {
                        _id: '$jiraProjectName',
                        logs: { $sum: '$timeSpentHours' }
                    }
                }],
                as: 'logs'
            }
        }, {
            $sort: {
                name: 1
            }
        }];
    }

    /**
     * @desc This function is being used to reset project headers in report
     * <AUTHOR>
     * @since 02/05/2024
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async resetProjectPersonHoursReportHeaders (req, locale) {
        const Validator = new UploadProjectHeaderValidator(null, locale);
        await Validator.validateUploadProjectHeaderFile(req.file);
        const parsedData = DownloadProjectPersonHoursReportService.getFileParsedData(req.file.buffer);
        await Constants.findOneAndUpdate({}, { projectSetForPersonHoursReport: parsedData }, { new: true });
        return true;
    }

    static getFileParsedData (buffer) {
        const projectSet = [];

        const workSheetsFromFile = xlsx.parse(buffer);
        const rows = workSheetsFromFile[0].data;

        for (let i = 1; i < rows.length; i++) {
            projectSet.push(rows[i][0]);
        }

        return projectSet;
    }
}

module.exports = DownloadProjectPersonHoursReportService;
