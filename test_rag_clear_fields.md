# RAG Report Field Clearing Fix

## Issue Description
When users tried to clear the Delivery Head comment/Tech Audit/Process Audit fields in the RAG Report by removing all content, the fields would not get cleared and would revert to their previous values.

## Root Cause Analysis
The issue was caused by three problems:

### 1. Frontend Issue (CustomInput.js)
In the `onSave` function, the code was using:
```javascript
value: inputValue || data
```
When `inputValue` was an empty string `""`, the `||` operator would fall back to the original `data` value, preventing the field from being cleared.

### 2. Backend Issue (ragValidator.js)
The validation was using:
```javascript
if (value == null) {
    throw new GeneralError(this.__(this.REQUIRED, 'Value'), 400);
}
```
This validation treated empty strings as invalid, preventing them from being saved to the database.

### 3. Helper Function Issue (Helper.js)
The `getNumbersOnly` function used for Tech Audit and Process Audit fields had a regex that didn't allow empty strings:
```javascript
const regex = /^[0-9]+([.][0-9])?$/;
// ...
} else if (!regex.test(value)) return (e.target.value = previousValues);
```
This would revert empty strings back to the previous value, preventing field clearing.

## Solution Implemented

### Frontend Fix 1 (CustomInput.js)
Changed the value assignment:
```javascript
// Before
value: inputValue || data

// After
value: inputValue !== undefined ? inputValue : data
```

### Frontend Fix 2 (Helper.js)
Updated the `getNumbersOnly` function to allow empty strings:
```javascript
// Added at the beginning of the function
if (value === '') {
  return; // Allow empty string to clear the field
}
```

### Backend Fix (ragValidator.js)
Updated the validation:
```javascript
// Before
if (value == null) {
    throw new GeneralError(this.__(this.REQUIRED, 'Value'), 400);
}

// After
if (value === null || value === undefined) {
    throw new GeneralError(this.__(this.REQUIRED, 'Value'), 400);
}
```

## Testing Steps
1. Login to Timesheet with valid credentials
2. Navigate to RAG Report
3. Navigate to Delivery Head comment/Tech Audit/Process Audit field
4. Enter a value/score into the field
5. Remove the full value entered
6. Verify that the field is now empty and the change is persisted

## Expected Result
The entered value should be completely removed from the field and the empty state should be saved to the database.
