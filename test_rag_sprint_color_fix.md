# RAG Report Sprint Number Color Fix

## Issue Description
When users select a client escalation value from the dropdown for team members in the RAG Report, the Sprint Number color was not changing to red immediately. The color would only update after a page refresh or data refetch.

## Root Cause Analysis
The issue was in the frontend `changeRowData` function in the RAG Report component. When a member client escalation was updated:

1. The local state was updated with the new escalation value
2. An API call was made to update the backend
3. However, the `overallRag` field (which determines the sprint number color) was not being recalculated in the frontend
4. The cell class rules for sprint number coloring depend on `params.data.overallRag` value
5. Since `overallRag` wasn't updated immediately, the color remained unchanged until the next data fetch

## Solution Implemented

### Frontend Fix (RagReport/index.js)

1. **Added `calculateOverallRag` function**: Created a frontend function that mirrors the backend RAG calculation logic:
   ```javascript
   calculateOverallRag(rowData) {
     // Calculate flags for open/close ratio, effort variance, and bugs
     // Check for client escalations and member escalations
     // Return appropriate RAG color (red/amber/green)
   }
   ```

2. **Enhanced `changeRowData` function**: Modified to recalculate `overallRag` when escalations are updated:
   ```javascript
   // Update team member escalation count
   if (param === 'memberClientEscalation' && row.member) {
     // Update team array with new escalation count
   }
   
   // Recalculate overall RAG color
   if (updatedRow._id && (param === 'memberClientEscalation' || param === 'clientEscalations')) {
     updatedRow.overallRag = this.calculateOverallRag(updatedRow);
   }
   ```

3. **Added grid refresh**: Force the AG-Grid to refresh cells after state update:
   ```javascript
   if (this.ragTableRef.current && (param === 'memberClientEscalation' || param === 'clientEscalations')) {
     this.ragTableRef.current.api.refreshCells();
   }
   ```

4. **Handle member row updates**: Added logic to update parent sprint rows when member escalations change:
   ```javascript
   // If updating a member row, also update the parent sprint row
   if (param === 'memberClientEscalation' && row._id) {
     // Find member row and update parent sprint's team array
     // Recalculate parent sprint's overall RAG color
   }
   ```

## Key Features of the Fix

1. **Immediate Visual Feedback**: Sprint number color changes immediately when escalation is selected
2. **Accurate Calculation**: Uses the same logic as backend for consistent results
3. **Handles All Cases**: Works for both direct sprint row updates and member row updates
4. **Grid Refresh**: Forces visual update of cell styling
5. **Maintains Data Integrity**: Still calls backend API to persist changes

## Testing Steps
1. Login to Timesheet with valid credentials
2. Navigate to RAG Report
3. Find a sprint with team members
4. Select a client escalation value > 0 for any team member
5. Observe that the Sprint Number color immediately changes to red
6. Verify the change persists after page refresh

## Expected Result
The Sprint Number should immediately turn red when any team member has a client escalation value greater than 0, providing instant visual feedback to users.
